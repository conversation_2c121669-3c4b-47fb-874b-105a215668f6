<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 80px;
      height: 100vh;
      background-color: #f0f0f0;
    }

    button.deposit-btn {
      position: relative;
      padding: 10px 20px;
      font-size: 16px;
      cursor: pointer;
    }

    button.deposit-btn .badge {
      position: absolute;
      bottom: 70%;
      left: 70%;
      width: 120px;
      height: 50px;
      border-radius: 25px 6px 25px 6px;
      background-color: red;
      transform-origin: bottom left;
      animation: deposit-btn-anim 3.33s linear infinite;
    }

    @keyframes deposit-btn-anim {
      0% { transform: scale(0) rotate(25deg); }
      7% { transform: scale(0.5813) rotate(-0.116deg); }
      9% { transform: scale(0.5541) rotate(-7.322deg); }
      14% { transform: scale(0.46) rotate(2.624deg); }
      16% { transform: scale(0.4742) rotate(5.502deg); }
      18% { transform: scale(0.4882) rotate(2.751deg); }
      20% { transform: scale(0.4882) rotate(0deg); }
      25% { transform: scale(0.4648) rotate(0deg); }
      32% { transform: scale(0.618) rotate(0deg); }
      38% { transform: scale(0.4562) rotate(0deg); }
      44% { transform: scale(0.5234) rotate(0deg); }
      50% { transform: scale(0.4882) rotate(0deg); }
      60% { transform: scale(0.4882) rotate(0deg); }
      65% { transform: scale(0.4648) rotate(0deg); }
      72% { transform: scale(0.618) rotate(0deg); }
      78% { transform: scale(0.4562) rotate(0deg); }
      84% { transform: scale(0.5234) rotate(0deg); }
      90% { transform: scale(0.4882) rotate(0deg); }
      100% { transform: scale(0.4882) rotate(0deg); }
    }


    button.post-btn {
      position: relative;
      border: none;
      outline: none;
      cursor: pointer;
      animation: post-anim 2.33s linear infinite;
    }

    button.post-btn .dot {
      position: absolute;
      top: 0;
      right: 0;
      transform: translate(50%, -50%);
      border-radius: 50%;
      background-color: red;
      animation: dot-anim 2.33s linear infinite;
    }

    @keyframes post-anim {
      0% { transform: translate(0, 0) rotate(0deg); }
      7% { transform: translate(0, -6px) rotate(0deg); }
      10% { transform: translate(0, -3px) rotate(0deg); }
      14% { transform: translate(0, 0) rotate(-0.98deg); }
      20% { transform: translate(0, 0) rotate(1.117deg); }
      24% { transform: translate(0, 0) rotate(-0.98deg); }
      28% { transform: translate(0, 0) rotate(1.117deg); }
      32% { transform: translate(0, 0) rotate(0deg); }
      100% { transform: translate(0, 0) rotate(0deg); }
    }

    @keyframes dot-anim {
      0% { transform: translate(50%, -50%) scale(1) rotate(0deg); }
      5% { transform: translate(50%, -50%) scale(1.0848) rotate(0deg); }
      11% { transform: translate(50%, -50%) scale(1) rotate(0deg); }
      28% { transform: translate(50%, -50%) scale(1) rotate(0deg); }
      32% { transform: translate(50%, -50%) scale(1.1447) rotate(-4.437deg); }
      37% { transform: translate(50%, -50%) scale(1.3515) rotate(2.43deg); }
      40% { transform: translate(50%, -50%) scale(1.431) rotate(-2.148deg); }
      41% { transform: translate(50%, -50%) scale(1.404) rotate(-4.437deg); }
      47% { transform: translate(50%, -50%) scale(1.1447) rotate(2.43deg); }
      51% { transform: translate(50%, -50%) scale(1) rotate(0deg); }
      77% { transform: translate(50%, -50%) scale(1) rotate(0deg); }
      81% { transform: translate(50%, -50%) scale(1.1447) rotate(-4.437deg); }
      85% { transform: translate(50%, -50%) scale(1.3515) rotate(2.43deg); }
      88% { transform: translate(50%, -50%) scale(1.431) rotate(-2.148deg); }
      90% { transform: translate(50%, -50%) scale(1.404) rotate(-4.437deg); }
      100% { transform: translate(50%, -50%) scale(1) rotate(0deg); }
    }
  </style>
</head>
<body>

  <button class="deposit-btn">
    <span>Deposit</span>
    <div class="badge"></div>
  </button>

  <button class="post-btn">
    <img class="post" src="./post.png" />
    <img class="dot" src="./dot.png" />
  </button>
</body>
</html>