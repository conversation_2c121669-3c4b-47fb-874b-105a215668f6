let timmer = null;
let token = null;

function generatePacScript(data) {
  const proxyNode = data.proxyNodes[data.proxyActiveIdx];
  const conditions = data.domain
    .map(d => `shExpMatch(host, "*.${d}") || shExpMatch(host, "${d}")`)
    .join(" || ");
  return `
    function FindProxyForURL(url, host) {
      if (${conditions}) {
        return "SOCKS5 ${proxyNode.host}:${proxyNode.port}";
      }
      return "DIRECT";
    }
  `;
}

function setProxy(enable, data) {
  const paramsValue = enable ? {
    mode: "pac_script",
    pacScript: {
      data: data ? generatePacScript(data) : {}
    }
  } : { mode: "direct" }

  chrome.proxy.settings.set({
    value: paramsValue,
    scope: "regular"
  }, () => {
    if (enable) {
      heartbeat();
      chrome.storage.local.set({ proxyEnabled: true });
      try {
        chrome.runtime.sendMessage({ type: "status-update", payload: "代理已启动" });
      } catch (e) {
        console.log("发送消息失败", e);
      }
    } else {
      stopHeartbeat();
      chrome.storage.local.set({ proxyEnabled: false });
      try {
        chrome.runtime.sendMessage({ type: "status-update", payload: "代理已关闭" });
      } catch (e) {
        console.log("发送消息失败", e);
      }
    }
  })
}

function checkProxyStatus() {
  fetch('https://vpn-api.ogweb.org/api/auth/ping', {
    method: 'POST',
    body: JSON.stringify({ token }),
    credentials: 'include'
  }).then((res) => res.json())
      .then((res) => {
        console.log("检测 IP：", res);
        const {data} = res;
        if(data.block_proxy){
          chrome.runtime.sendMessage({ type: "status-close", payload: "status error" });
        }
      })
      .catch((e) => {
        chrome.runtime.sendMessage({ type: "status-close", payload: "status error" });
      });
}

function heartbeat() {
  timmer = setInterval(checkProxyStatus, 5 * 1000);

  chrome.proxy.settings.get({'incognito': false}, function(config) {
      console.log('OG-proxy startup, current proxy config:');
      console.log(JSON.stringify(config));
    }
  );
}

function stopHeartbeat() {
  clearInterval(timmer);
  timmer = null;
}

function closeProxy() {
  chrome.storage.local.set({ closeTime: new Date().toString() });
  setProxy(false);
}

function openProxy() {
  chrome.storage.local.get(['token', 'domain', 'proxyNodes', 'proxyActiveIdx'], (data) => {
    token = data.token;
    setProxy(true, data);
  });
}

chrome.proxy.settings.get({'incognito': false}, function(config) {
    console.log('OG-proxy startup, current proxy config:');
    console.log(JSON.stringify(config));
  }
);

chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  if (msg.type === "connect") {
    openProxy();
    sendResponse({ success: true });
  } else if (msg.type === "close") {
    closeProxy();
    sendResponse({ success: true });
  }
});

chrome.runtime.onStartup.addListener(() => {
  console.log('OG-proxy startup, starting heartbeat');
  chrome.storage.local.get(["proxyEnabled", "token"], (data) => {
    token = data.token;
    if (data.proxyEnabled) {
      closeProxy();
    }
  });
});

chrome.runtime.onSuspend.addListener(() => {
  closeProxy();
});

chrome.action.onClicked.addListener((tab) => {
  console.log('Extension icon clicked');
  alert('Extension icon clicked');
  // This keeps the service worker alive
});

chrome.windows.onRemoved.addListener(async () => {
  const remainingWindows = await chrome.windows.getAll();
  if (remainingWindows.length === 0) {
    closeProxy();
  }
});
