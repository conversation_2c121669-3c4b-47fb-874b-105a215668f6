document.addEventListener('DOMContentLoaded', () => {
    const step1 = document.getElementById("step1");
    const step2 = document.getElementById("step2");

    const codeInput = document.getElementById("token");
    const switchBtn = document.getElementById("switch-btn");

    const proxySelect = document.getElementById("proxy-select");
    const proxyNodes = document.getElementById("proxy-nodes");

    const loginBtn = document.getElementById("loginBtn");
    const exitBtn = document.getElementById("exitBtn");
    const statusText = document.getElementById("status");

    function generateNodes() {
        chrome.storage.local.get(["proxyAuth", "proxyNodes", "proxyActiveIdx"], (data) => {
            if (!data.proxyAuth) return;

            const nodes = data.proxyNodes;
            const activeIdx = data.proxyActiveIdx;

            // 生成代理节点列表
            proxyNodes.innerHTML = nodes.map((node, idx) => {
                const active = idx === activeIdx ? "active" : "";
                return `<li class="${active}">${node.region} ${node.remark}</li>`;
            }).join("");

            // 代理li点击事件
            proxyNodes.addEventListener("click", (e) => {
                proxyNodes.classList.remove("show");

                if (e.target.nodeName !== "LI") return;

                const idx = Array.from(proxyNodes.children).indexOf(e.target);

                if (idx === activeIdx) return;

                chrome.storage.local.set({proxyActiveIdx: idx}, () => {
                    updateStatus();
                });
            });
        });
    }

    function updateStatus() {
        chrome.storage.local.get(["proxyEnabled", "proxyAuth", "proxyConfig", "proxyNodes", "proxyActiveIdx"], (data) => {
            if (data.proxyAuth) {
                const node = data.proxyNodes[data.proxyActiveIdx ?? 0];
                const connectStatusText = switchBtn.querySelector("span");

                step1.style.display = 'none';
                step2.style.display = 'block';
                proxySelect.textContent = `当前节点：${node.region} ${node.remark}`;

                if (data.proxyEnabled) {
                    switchBtn.style.color = 'red';
                    switchBtn.style.borderColor = 'red';
                    statusText.textContent = "代理状态：已启用";
                    connectStatusText && (connectStatusText.textContent = "点击断开");
                } else {
                    switchBtn.style.color = 'green';
                    switchBtn.style.borderColor = 'green';
                    statusText.textContent = "代理状态：未启用";
                    connectStatusText && (connectStatusText.textContent = "点击连接");
                }
            } else {
                step1.style.display = 'block';
                step2.style.display = 'none';
                statusText.textContent = "代理状态：未启用";
            }
        });
    }

    function parseAddress(node) {
        const url = new URL(node.address);
        return {
            ...node,
            host: url.hostname,
            port: url.port || (url.protocol === 'https:' ? 1080 : 1080)
        };
    }

    function connect() {
        chrome.runtime.sendMessage({type: "connect"}, (res) => {
            if (res.success) {
                alert('登录成功，域名分流代理已启用');
                updateStatus();
            } else {
                alert(res.message || "授权失败");
            }
        });
    }

    function close() {
        chrome.runtime.sendMessage({type: "close"}, () => {
            alert("已关闭代理")
            updateStatus();
        });
    }

    loginBtn.onclick = () => {
        const token = codeInput.value.trim();
        if (!token) {
            alert('请输入授权码');
            return;
        }
        fetch('https://vpn-api.ogweb.org/api/auth/verify', {
            method: 'POST',
            body: JSON.stringify({ token: token, region: 'CN' }),
            credentials: 'include'
        })
            .then(res => res.json())
            .then(({data}) => {
                if (data.nodes && data.nodes.length > 0) {
                    const activeIdx = 0;
                    const formatNodes = (data.nodes ?? []).map(parseAddress);
                    const {host, port} = formatNodes[activeIdx];
                    const proxy = {
                        host: host,
                        port: port,
                        protocol: "socks5"
                    };
                    chrome.storage.local.set({
                        token,
                        domain: data.domain,
                        proxyNodes: formatNodes,
                        proxyActiveIdx: activeIdx,
                        proxyConfig: proxy,
                        proxyAuth: true,
                        proxyEnabled: false,
                    }, () => {
                        generateNodes();
                        updateStatus();
                    });
                } else {
                    alert("授权码验证失败，未返回有效节点");
                }
            }).catch(error => {
                console.error("登录请求失败:", error);
                alert("请求失败，请检查网络");
            });

    };

    exitBtn.onclick = () => {
        close();
        chrome.storage.local.set({
            token: "",
            proxyAuth: false,
            proxyEnabled: false,
        }, () => {
            updateStatus();
        });
    };

    switchBtn.onclick = () => {
        chrome.storage.local.get(["proxyEnabled"], (data) => {
            if (data.proxyEnabled) {
                close()
            } else {
                connect()
            }
        })
    }

    proxySelect.onclick = () => {
        proxyNodes.classList.toggle("show");
    }

    document.addEventListener("click", (e) => {
        if (!proxySelect.contains(e.target)) {
            proxyNodes.classList.remove("show");
        }
    });

    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === "status-update") {
            updateStatus();
        }
    });

    updateStatus();
    generateNodes();
});




